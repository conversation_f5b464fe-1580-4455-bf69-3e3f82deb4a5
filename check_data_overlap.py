#!/usr/bin/env python3
"""
检查训练集和验证集的时间重叠问题
"""

import pandas as pd
import numpy as np
from myinformer_IAS import TrainingConfig

def analyze_data_overlap():
    """分析训练集和验证集的重叠情况"""
    config = TrainingConfig()
    
    print("=" * 60)
    print("数据重叠详细分析")
    print("=" * 60)
    
    # 加载数据
    train_df = pd.read_csv(config.train_file_path)
    val_df = pd.read_csv(config.validation_file_path)
    
    print(f"训练集: {config.train_file_path}")
    print(f"验证集: {config.validation_file_path}")
    print(f"时间列: {config.time_column}")
    
    # 基本信息
    print(f"\n基本信息:")
    print(f"  训练集大小: {len(train_df)} 行")
    print(f"  验证集大小: {len(val_df)} 行")
    
    # 检查时间列
    if config.time_column not in train_df.columns:
        print(f"错误: 训练集中没有时间列 '{config.time_column}'")
        print(f"可用列: {list(train_df.columns)}")
        return
    
    if config.time_column not in val_df.columns:
        print(f"错误: 验证集中没有时间列 '{config.time_column}'")
        print(f"可用列: {list(val_df.columns)}")
        return
    
    # 时间范围分析
    print(f"\n时间范围分析:")
    train_times = pd.to_datetime(train_df[config.time_column])
    val_times = pd.to_datetime(val_df[config.time_column])
    
    print(f"  训练集时间范围: {train_times.min()} 到 {train_times.max()}")
    print(f"  验证集时间范围: {val_times.min()} 到 {val_times.max()}")
    
    # 重叠分析
    train_time_set = set(train_df[config.time_column])
    val_time_set = set(val_df[config.time_column])
    overlap = train_time_set.intersection(val_time_set)
    
    print(f"\n重叠分析:")
    print(f"  训练集唯一时间点: {len(train_time_set)}")
    print(f"  验证集唯一时间点: {len(val_time_set)}")
    print(f"  重叠时间点数量: {len(overlap)}")
    print(f"  重叠比例 (相对训练集): {len(overlap)/len(train_time_set)*100:.2f}%")
    print(f"  重叠比例 (相对验证集): {len(overlap)/len(val_time_set)*100:.2f}%")
    
    if len(overlap) > 0:
        print(f"\n⚠️  发现严重的数据泄露问题!")
        print(f"训练集和验证集有 {len(overlap)} 个重叠的时间点")
        
        # 显示一些重叠的时间点
        overlap_list = sorted(list(overlap))
        print(f"\n重叠时间点示例 (前10个):")
        for i, time_point in enumerate(overlap_list[:10]):
            print(f"  {i+1}. {time_point}")
        
        if len(overlap_list) > 10:
            print(f"  ... 还有 {len(overlap_list)-10} 个重叠时间点")
    
    # 段标记分析
    if config.segment_marker_column in train_df.columns and config.segment_marker_column in val_df.columns:
        print(f"\n段标记分析:")
        
        # 获取段的起始和结束
        train_segments = train_df[train_df[config.segment_marker_column].notna()]
        val_segments = val_df[val_df[config.segment_marker_column].notna()]
        
        print(f"  训练集段标记: {len(train_segments)} 个")
        print(f"  验证集段标记: {len(val_segments)} 个")
        
        if len(train_segments) > 0:
            print(f"  训练集段标记值: {sorted(train_segments[config.segment_marker_column].unique())}")
        
        if len(val_segments) > 0:
            print(f"  验证集段标记值: {sorted(val_segments[config.segment_marker_column].unique())}")
    
    # 建议修复方案
    print(f"\n" + "=" * 60)
    print("修复建议")
    print("=" * 60)
    
    if len(overlap) > 0:
        print("发现数据泄露，建议采取以下措施:")
        print("1. 重新分割数据，确保训练集和验证集在时间上完全分离")
        print("2. 使用时间顺序分割：训练集使用较早的时间段，验证集使用较晚的时间段")
        print("3. 或者使用段级别分割：确保同一段的数据不会同时出现在训练集和验证集中")
        print("4. 检查数据预处理流程，确保分割逻辑正确")
        
        # 提供具体的时间分割建议
        all_times = sorted(train_time_set.union(val_time_set))
        split_point = len(all_times) * 0.8  # 80%用于训练
        suggested_split_time = all_times[int(split_point)]
        
        print(f"\n建议的时间分割点: {suggested_split_time}")
        print(f"  训练集: 时间 <= {suggested_split_time}")
        print(f"  验证集: 时间 > {suggested_split_time}")
    else:
        print("✓ 未发现时间重叠，数据分割正确")
    
    return len(overlap) == 0

def check_segment_based_split():
    """检查是否可以基于段进行分割"""
    config = TrainingConfig()
    
    print(f"\n" + "=" * 60)
    print("段级别分割可行性分析")
    print("=" * 60)
    
    try:
        train_df = pd.read_csv(config.train_file_path)
        val_df = pd.read_csv(config.validation_file_path)
        
        if config.segment_marker_column not in train_df.columns:
            print(f"训练集中没有段标记列 '{config.segment_marker_column}'")
            return False
        
        # 分析段的分布
        train_segments = train_df[train_df[config.segment_marker_column].notna()]
        val_segments = val_df[val_df[config.segment_marker_column].notna()]
        
        if len(train_segments) == 0 or len(val_segments) == 0:
            print("段标记数据不足，无法进行段级别分析")
            return False
        
        # 获取段的唯一标识
        train_segment_ids = set(train_segments[config.segment_marker_column].unique())
        val_segment_ids = set(val_segments[config.segment_marker_column].unique())
        
        segment_overlap = train_segment_ids.intersection(val_segment_ids)
        
        print(f"段分布分析:")
        print(f"  训练集段数: {len(train_segment_ids)}")
        print(f"  验证集段数: {len(val_segment_ids)}")
        print(f"  重叠段数: {len(segment_overlap)}")
        
        if len(segment_overlap) > 0:
            print(f"  重叠段标识: {sorted(segment_overlap)}")
            print("⚠️  段级别也存在重叠，需要重新分割")
            return False
        else:
            print("✓ 段级别无重叠，分割正确")
            return True
            
    except Exception as e:
        print(f"段级别分析失败: {e}")
        return False

if __name__ == "__main__":
    print("开始数据重叠检查...")
    
    # 分析时间重叠
    no_time_overlap = analyze_data_overlap()
    
    # 分析段重叠
    no_segment_overlap = check_segment_based_split()
    
    print(f"\n" + "=" * 60)
    print("最终结论")
    print("=" * 60)
    
    if no_time_overlap and no_segment_overlap:
        print("🎉 数据分割正确，无泄露风险")
    else:
        print("⚠️  存在数据泄露风险，需要重新分割数据")
        print("\n立即行动项:")
        print("1. 停止当前训练")
        print("2. 重新分割训练集和验证集")
        print("3. 确保时间和段级别都无重叠")
        print("4. 重新运行检查脚本验证")
