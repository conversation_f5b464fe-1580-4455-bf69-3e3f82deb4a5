#!/usr/bin/env python3
"""
修复数据泄露问题 - 重新分割训练集和验证集
确保时间上完全分离，无重叠
"""

import pandas as pd
import numpy as np
import os
from myinformer_IAS import TrainingConfig

def backup_original_files():
    """备份原始文件"""
    config = TrainingConfig()
    
    train_backup = config.train_file_path.replace('.csv', '_backup_original.csv')
    val_backup = config.validation_file_path.replace('.csv', '_backup_original.csv')
    
    if not os.path.exists(train_backup):
        train_df = pd.read_csv(config.train_file_path)
        train_df.to_csv(train_backup, index=False, encoding='utf-8-sig')
        print(f"✓ 训练集备份到: {train_backup}")
    
    if not os.path.exists(val_backup):
        val_df = pd.read_csv(config.validation_file_path)
        val_df.to_csv(val_backup, index=False, encoding='utf-8-sig')
        print(f"✓ 验证集备份到: {val_backup}")

def load_all_data():
    """加载所有数据并合并"""
    config = TrainingConfig()
    
    print("加载原始数据...")
    train_df = pd.read_csv(config.train_file_path)
    val_df = pd.read_csv(config.validation_file_path)
    
    print(f"训练集大小: {len(train_df)}")
    print(f"验证集大小: {len(val_df)}")
    
    # 合并所有数据
    all_data = pd.concat([train_df, val_df], ignore_index=True)
    print(f"合并后总数据量: {len(all_data)}")
    
    # 去重（基于时间列）
    before_dedup = len(all_data)
    all_data = all_data.drop_duplicates(subset=[config.time_column], keep='first')
    after_dedup = len(all_data)
    
    print(f"去重前: {before_dedup} 行")
    print(f"去重后: {after_dedup} 行")
    print(f"删除重复: {before_dedup - after_dedup} 行")
    
    return all_data, config

def time_based_split(all_data, config, train_ratio=0.8):
    """基于时间的分割方法"""
    print(f"\n使用时间分割方法 (训练集比例: {train_ratio})")
    
    # 按时间排序
    all_data[config.time_column] = pd.to_datetime(all_data[config.time_column])
    all_data = all_data.sort_values(config.time_column).reset_index(drop=True)
    
    # 计算分割点
    split_idx = int(len(all_data) * train_ratio)
    
    train_data = all_data.iloc[:split_idx].copy()
    val_data = all_data.iloc[split_idx:].copy()
    
    print(f"分割点索引: {split_idx}")
    print(f"训练集: {len(train_data)} 行 (时间范围: {train_data[config.time_column].min()} 到 {train_data[config.time_column].max()})")
    print(f"验证集: {len(val_data)} 行 (时间范围: {val_data[config.time_column].min()} 到 {val_data[config.time_column].max()})")
    
    # 验证无重叠
    train_times = set(train_data[config.time_column].astype(str))
    val_times = set(val_data[config.time_column].astype(str))
    overlap = train_times.intersection(val_times)
    
    if len(overlap) == 0:
        print("✓ 时间分割成功，无重叠")
        return train_data, val_data
    else:
        print(f"✗ 仍有 {len(overlap)} 个重叠时间点")
        return None, None

def segment_based_split(all_data, config, train_ratio=0.8):
    """基于段的分割方法"""
    print(f"\n使用段分割方法 (训练集比例: {train_ratio})")
    
    # 获取所有段标记
    segment_markers = all_data[all_data[config.segment_marker_column].notna()]
    
    if len(segment_markers) == 0:
        print("没有找到段标记，无法使用段分割")
        return None, None
    
    # 提取段号
    def extract_segment_number(marker):
        try:
            if '段' in str(marker):
                # 提取段号，例如从"段11起点"中提取"11"
                import re
                match = re.search(r'段(\d+)', str(marker))
                if match:
                    return int(match.group(1))
            return None
        except:
            return None
    
    segment_markers['段号'] = segment_markers[config.segment_marker_column].apply(extract_segment_number)
    unique_segments = sorted([s for s in segment_markers['段号'].unique() if s is not None])
    
    print(f"发现 {len(unique_segments)} 个唯一段: {unique_segments}")
    
    # 分割段
    split_idx = int(len(unique_segments) * train_ratio)
    train_segments = unique_segments[:split_idx]
    val_segments = unique_segments[split_idx:]
    
    print(f"训练集段: {train_segments}")
    print(f"验证集段: {val_segments}")
    
    # 根据段号分割数据
    train_data_list = []
    val_data_list = []
    
    for segment_num in unique_segments:
        # 找到该段的起点和终点
        segment_start = segment_markers[
            (segment_markers['段号'] == segment_num) & 
            (segment_markers[config.segment_marker_column].str.contains('起点', na=False))
        ]
        segment_end = segment_markers[
            (segment_markers['段号'] == segment_num) & 
            (segment_markers[config.segment_marker_column].str.contains('终点', na=False))
        ]
        
        if len(segment_start) > 0 and len(segment_end) > 0:
            start_idx = segment_start.index[0]
            end_idx = segment_end.index[0]
            
            # 确保索引顺序正确
            if start_idx > end_idx:
                start_idx, end_idx = end_idx, start_idx
            
            # 提取该段的数据
            segment_data = all_data.iloc[start_idx:end_idx+1].copy()
            
            if segment_num in train_segments:
                train_data_list.append(segment_data)
            else:
                val_data_list.append(segment_data)
    
    # 合并数据
    if train_data_list and val_data_list:
        train_data = pd.concat(train_data_list, ignore_index=True)
        val_data = pd.concat(val_data_list, ignore_index=True)
        
        print(f"训练集: {len(train_data)} 行")
        print(f"验证集: {len(val_data)} 行")
        
        # 验证无重叠
        train_times = set(train_data[config.time_column].astype(str))
        val_times = set(val_data[config.time_column].astype(str))
        overlap = train_times.intersection(val_times)
        
        if len(overlap) == 0:
            print("✓ 段分割成功，无重叠")
            return train_data, val_data
        else:
            print(f"✗ 仍有 {len(overlap)} 个重叠时间点")
            return None, None
    else:
        print("段分割失败，数据不足")
        return None, None

def save_new_splits(train_data, val_data, config):
    """保存新的分割结果"""
    print(f"\n保存新的数据分割...")
    
    # 保存训练集
    train_data.to_csv(config.train_file_path, index=False, encoding='utf-8-sig')
    print(f"✓ 新训练集保存到: {config.train_file_path}")
    print(f"  大小: {len(train_data)} 行")
    
    # 保存验证集
    val_data.to_csv(config.validation_file_path, index=False, encoding='utf-8-sig')
    print(f"✓ 新验证集保存到: {config.validation_file_path}")
    print(f"  大小: {len(val_data)} 行")

def verify_fix():
    """验证修复效果"""
    print(f"\n验证修复效果...")
    
    config = TrainingConfig()
    train_df = pd.read_csv(config.train_file_path)
    val_df = pd.read_csv(config.validation_file_path)
    
    # 检查时间重叠
    train_times = set(train_df[config.time_column])
    val_times = set(val_df[config.time_column])
    overlap = train_times.intersection(val_times)
    
    print(f"验证结果:")
    print(f"  训练集大小: {len(train_df)}")
    print(f"  验证集大小: {len(val_df)}")
    print(f"  时间重叠: {len(overlap)} 个")
    
    if len(overlap) == 0:
        print("🎉 修复成功！无时间重叠")
        return True
    else:
        print(f"❌ 修复失败，仍有 {len(overlap)} 个重叠时间点")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("数据泄露修复程序")
    print("=" * 60)
    
    # 1. 备份原始文件
    print("1. 备份原始文件...")
    backup_original_files()
    
    # 2. 加载所有数据
    print("\n2. 加载和合并数据...")
    all_data, config = load_all_data()
    
    # 3. 尝试时间分割
    print("\n3. 尝试时间分割...")
    train_data, val_data = time_based_split(all_data, config, train_ratio=0.8)
    
    # 4. 如果时间分割失败，尝试段分割
    if train_data is None or val_data is None:
        print("\n4. 时间分割失败，尝试段分割...")
        train_data, val_data = segment_based_split(all_data, config, train_ratio=0.8)
    
    # 5. 保存结果
    if train_data is not None and val_data is not None:
        print("\n5. 保存新的数据分割...")
        save_new_splits(train_data, val_data, config)
        
        # 6. 验证修复效果
        print("\n6. 验证修复效果...")
        success = verify_fix()
        
        if success:
            print("\n" + "=" * 60)
            print("🎉 数据泄露修复完成！")
            print("=" * 60)
            print("下一步:")
            print("1. 重新运行 comprehensive_data_leakage_check.py 验证")
            print("2. 开始重新训练模型")
            print("3. 对比修复前后的模型性能")
        else:
            print("\n" + "=" * 60)
            print("❌ 修复失败")
            print("=" * 60)
            print("建议:")
            print("1. 检查数据源是否有问题")
            print("2. 考虑手动分割数据")
            print("3. 联系数据提供方确认数据结构")
    else:
        print("\n❌ 所有分割方法都失败了")
        print("建议手动检查数据结构和分割逻辑")

if __name__ == "__main__":
    main()
